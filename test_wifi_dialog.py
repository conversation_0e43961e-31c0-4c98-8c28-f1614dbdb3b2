#!/usr/bin/env python3
"""
Test script for the WiFi password dialog with eye icon toggle
"""

import gi
gi.require_version("Gtk", "3.0")
from gi.repository import Gtk, GLib

from fabric import Application
from widgets.wifi_password_dialog import WiFiPasswordDialog


def on_connect(ssid, password):
    print(f"✅ Connect to '{ssid}' with password: '{password}'")
    print("🔒 Password visibility was toggled using eye icon")
    Gtk.main_quit()


def on_cancel():
    print("❌ Connection cancelled by user")
    Gtk.main_quit()


def main():
    print("🔧 Testing WiFi Password Dialog with Eye Icon Toggle")
    print("👁️  Click the eye icon to toggle password visibility")
    print("⌨️  Press Enter to connect, Escape to cancel")
    print("=" * 50)

    # Create and show the dialog
    dialog = WiFiPasswordDialog(
        ssid="free_wifi@DH",
        on_connect_callback=on_connect,
        on_cancel_callback=on_cancel
    )

    dialog.show_dialog()

    # Start the GTK main loop
    Gtk.main()


if __name__ == "__main__":
    main()
