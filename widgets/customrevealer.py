import gi
gi.require_version("Gtk", "3.0")
from gi.repository import Gtk, GLib

# Centralized animation manager with batched rendering
class AnimationManager:
    _instance = None
    _animating_widgets = set()
    _timer_id = None
    _containers_to_redraw = set()

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def add_widget(self, widget):
        self._animating_widgets.add(widget)
        if self._timer_id is None:
            self._timer_id = GLib.timeout_add(22, self._animate_all)  # ~45 FPS

    def remove_widget(self, widget):
        self._animating_widgets.discard(widget)
        if not self._animating_widgets and self._timer_id:
            # Stop timer when no widgets are animating
            GLib.source_remove(self._timer_id)
            self._timer_id = None
            # Clear any pending redraws
            self._containers_to_redraw.clear()

    def _animate_all(self):
        # Clear previous frame's redraw queue
        self._containers_to_redraw.clear()

        # Calculate new positions for all widgets in batch
        widgets_to_remove = []
        for widget in list(self._animating_widgets):
            if not widget._calculate_position():
                widgets_to_remove.append(widget)
            else:
                # Queue container for redraw (batched)
                container = widget._get_container_for_redraw()
                if container:
                    self._containers_to_redraw.add(container)

        # Apply all position changes at once
        for widget in self._animating_widgets:
            widget._apply_position()

        # Single redraw call per container (batched rendering)
        for container in self._containers_to_redraw:
            container.queue_draw()

        # Remove completed animations
        for widget in widgets_to_remove:
            self.remove_widget(widget)

        return len(self._animating_widgets) > 0  # Continue if widgets remain

    def _get_optimal_interval(self):
        """Calculate optimal frame interval based on widget count"""
        widget_count = len(self._animating_widgets)
        if widget_count <= 1:
            return 22  # ~45 FPS for single widget
        elif widget_count <= 3:
            return 33  # ~30 FPS for 2-3 widgets
        elif widget_count <= 6:
            return 50  # ~20 FPS for 4-6 widgets
        else:
            return 67  # ~15 FPS for 7+ widgets

    def _start_timer(self):
        """Start timer with optimal frame rate"""
        interval = self._get_optimal_interval()
        self._timer_id = GLib.timeout_add(interval, self._animate_all)

    def _adjust_frame_rate(self):
        """Dynamically adjust frame rate based on current widget count"""
        if not self._timer_id:
            return

        new_interval = self._get_optimal_interval()

        # Restart timer with new interval
        GLib.source_remove(self._timer_id)
        self._timer_id = GLib.timeout_add(new_interval, self._animate_all)

class SlideRevealer(Gtk.Overlay):
    def __init__(self, child: Gtk.Widget, direction="right", duration=300, size=None):
        super().__init__()

        self.child = child
        self.direction = direction
        self.duration = duration
        self.fixed_size = size  # (width, height) tuple or None for auto-size
        self._revealed = False
        self._animating = False
        self._start_time = None
        self._show_animation = False
        self._pending_position = None  # Store calculated position for batched application

        # Set up the overlay with a fixed container for the animation
        self._fixed = Gtk.Fixed()
        self._fixed.set_has_window(False)
        self._fixed.add(child)
        self.add_overlay(self._fixed)

        # Set initial size based on size argument
        if self.fixed_size:
            # Use provided fixed size
            self.set_size_request(self.fixed_size[0], self.fixed_size[1])
            child.hide()
            self.show_all()
        else:
            # Use auto-sizing based on child allocation
            child.connect("size-allocate", self._on_size_allocate)
            child.hide()
            self.show_all()

    def _on_size_allocate(self, _widget, allocation):
        # Only called when using auto-sizing (fixed_size is None)
        # Avoid unnecessary size requests if size hasn't changed
        if not self.fixed_size:
            current_req = self.get_size_request()
            if current_req[0] != allocation.width or current_req[1] != allocation.height:
                self.set_size_request(allocation.width, allocation.height)

    def set_reveal_child(self, reveal: bool):
        if reveal:
            self.reveal()
        else:
            self.hide()

    def reveal(self):
        if self._revealed or self._animating:
            return
        self._revealed = True
        self._start_animation(show=True)

    def hide(self):
        if not self._revealed or self._animating:
            return
        self._revealed = False
        self._start_animation(show=False)

    def _start_animation(self, show: bool):
        # Stop any existing animation
        if self._animating:
            AnimationManager.get_instance().remove_widget(self)

        # Cache dimensions once to avoid repeated allocation calls
        self._cached_dimensions = self._get_dimensions()
        if self._cached_dimensions[0] == 0 or self._cached_dimensions[1] == 0:
            # Skip animation if no valid dimensions
            self._animating = False
            return

        self._start_time = GLib.get_monotonic_time()
        self._animating = True
        self._show_animation = show

        # Position child offscreen
        if show:
            self.child.show()
            self._fixed.move(self.child, *self._get_offscreen_pos_cached())

        # Add to shared animation manager instead of creating individual timer
        AnimationManager.get_instance().add_widget(self)

    def _calculate_position(self):
        """Calculate new position - called by animation manager (no drawing yet)"""
        if not self._animating:
            return False

        elapsed = (GLib.get_monotonic_time() - self._start_time) / 1000
        t = min(elapsed / self.duration, 1.0)

        # Simplified easing - avoid redundant calculations
        if self._show_animation:
            eased = 1 - (1 - t) ** 3  # Inline ease_out_cubic
        else:
            eased = t ** 3  # Ease in cubic for hide animation

        # Store position for batched application
        self._pending_position = self._get_position_at_progress_cached(eased)

        if t >= 1.0:
            self._animating = False
            self._cached_dimensions = None  # Clear cache
            if not self._show_animation:
                # Hide after position is applied
                GLib.idle_add(lambda: self.child.hide())
            return False  # Animation complete
        return True  # Continue animation

    def _apply_position(self):
        """Apply the calculated position - called after all positions are calculated"""
        if self._pending_position:
            x, y = self._pending_position
            self._fixed.move(self.child, x, y)
            self._pending_position = None

    def _get_container_for_redraw(self):
        """Return the container that needs redrawing"""
        return self  # The SlideRevealer itself needs redrawing

    def _get_dimensions(self):
        """Get dimensions from fixed size or child allocation"""
        if self.fixed_size:
            return self.fixed_size
        else:
            alloc = self.child.get_allocation()
            return alloc.width, alloc.height

    def _get_offscreen_pos_cached(self):
        """Get offscreen position using cached dimensions"""
        w, h = self._cached_dimensions
        if self.direction == "left":
            return -w, 0
        elif self.direction == "right":
            return w, 0
        elif self.direction == "top":
            return 0, -h
        elif self.direction == "bottom":
            return 0, h
        return 0, 0

    def _get_position_at_progress_cached(self, progress):
        """Get position at progress using cached dimensions"""
        w, h = self._cached_dimensions
        if self._show_animation:
            # Showing animation: slide from offscreen to onscreen (0,0)
            if self.direction == "left":
                return int(-w + w * progress), 0
            elif self.direction == "right":
                return int(w - w * progress), 0
            elif self.direction == "top":
                return 0, int(-h + h * progress)
            elif self.direction == "bottom":
                return 0, int(h - h * progress)
        else:
            # Hiding animation: slide from onscreen (0,0) to offscreen
            if self.direction == "left":
                return int(-w * progress), 0  # Slide left (negative x)
            elif self.direction == "right":
                return int(w * progress), 0   # Slide right (positive x)
            elif self.direction == "top":
                return 0, int(-h * progress)  # Slide up (negative y)
            elif self.direction == "bottom":
                return 0, int(h * progress)   # Slide down (positive y)
        return 0, 0

    def set_slide_direction(self, direction):
        """Change the slide direction"""
        self.direction = direction

    def is_revealed(self):
        """Check if the revealer is currently revealed"""
        return self._revealed

    def is_animating(self):
        """Check if the revealer is currently animating"""
        return self._animating

    def get_child_revealed(self):
        """Get the current reveal state (compatible with Fabric Revealer API)"""
        return self._revealed

    def stop_animation(self):
        """Stop any ongoing animation"""
        if self._animating:
            AnimationManager.get_instance().remove_widget(self)
            self._animating = False
            self._cached_dimensions = None

    def destroy(self):
        """Clean up resources"""
        self.stop_animation()
        super().destroy()
