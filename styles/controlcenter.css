#control-center-menu {
  background-color: transparent;
  border-radius: 15px;
  box-shadow: none;
  margin: 0;
}

#control-center-widgets {
  background-color: alpha(#000, 0.3);
  box-shadow: inset 0 0 0 1px alpha(#aaa, 0.4);
  border: 1px solid alpha(#111, 0.4);
  border-radius: 1.25rem;
  padding: 0.5rem;
}

/* Widgets */
#wifi-widget,
#bluetooth-widget {
  padding: 10px;
}

.icon {
  background-color: #2369ff;
  font-size: 15px;
  padding: 20px 20px;
}

#bluetooth-widget-label,
#wifi-widget-label {
  font-size: 10px;
  margin-left: 5px;
  font-weight: 400;
  color: #aaa;
}

/* WiFi Password Dialog */
#wifi-password-dialog {
  background-color: transparent;
}

#wifi-dialog-background {
  background-color: rgba(40, 40, 40, 0.95);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
  padding: 24px;
  min-width: 300px;
}


#wifi-dialog-icon {
  color: #007AFF;
  margin-bottom: 8px;
}

#wifi-dialog-title {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
}

#wifi-dialog-error {
  color: #ff4444;
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 8px;
}

#wifi-dialog-password-container {
  margin: 16px 0;
}

#wifi-dialog-password-label {
  color: #ffffff;
  font-size: 13px;
  font-weight: 500;
  margin-bottom: 4px;
}

#wifi-dialog-password-entry {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  padding: 8px 12px;
  color: #ffffff;
  font-size: 13px;
}

#wifi-dialog-password-entry:focus {
  border-color: #007AFF;
  box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.3);
  background-color: rgba(255, 255, 255, 0.15);
}

#wifi-dialog-show-password-box {
  margin-top: 8px;
}

#wifi-dialog-show-password-button {
  background-color: transparent;
  border: none;
  padding: 4px;
  border-radius: 4px;
  min-width: 24px;
  min-height: 24px;
}

#wifi-dialog-show-password-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

#wifi-dialog-show-password-button image {
  color: #ffffff;
}

#wifi-dialog-show-password-label {
  color: #ffffff;
  font-size: 12px;
}

#wifi-dialog-button-box {
  margin-top: 20px;
}

#wifi-dialog-cancel-button,
#wifi-dialog-join-button {
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  min-width: 80px;
  min-height: 30px;
}

#wifi-dialog-cancel-button {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

#wifi-dialog-cancel-button:hover {
  background-color: rgba(255, 255, 255, 0.15);
}

#wifi-dialog-join-button {
  background-color: #007AFF;
  border: 1px solid #007AFF;
  color: #ffffff;
}

#wifi-dialog-join-button:hover {
  background-color: #0056CC;
  border-color: #0056CC;
}

#bluetooth-widget-title {
  font-size: 12px;
  font-weight: 500;
  color: #fff;
}

#bluetooth-widget-top {
  padding-bottom: 10px;
  border-bottom: 1px solid alpha(#aaa, 0.3);
}

#device-icon {
  border-radius: 50%;
  padding: 5px;
  margin-bottom: 2.5px;
  margin-top: 2.5px;
  margin-right: 10px;
  background-color: #555;
}

#devices-title {
  color: alpha(#fff, 0.6);
}

#device-icon.paired {
  background-color: #888;
}

#device-icon.connected {
  background-color: #2369ff;
}

#toggle-button {
  min-width: 40px;
  min-height: 20px;
  background-color: var(--surface);
  border-radius: 15px;
  padding: 2px;
  transition: background-color 0.3s ease;
}
#toggle-button slider {
  background-color: var(--primary);
  border-radius: 16px;
  min-width: 16px;
  min-height: 8px;
  transition:
    background-color 0.1s cubic-bezier(0.5, 0.25, 0, 1.25),
    transform 0.25s cubic-bezier(0.5, 0.25, 0, 1.25);
}

#toggle-button:checked {
  background-color: var(--primary);
}

#toggle-button:checked slider {
  background-color: var(--shadow);
}

#toggle-button:checked image {
  color: var(--shadow);
}

.menu {
  margin: 5px;
  background-color: alpha(#000, 0.3);
  border: 0.5px solid alpha(#888, 0.4);
  box-shadow: inset 0 0 200px 0 alpha(#111, 0.3);
  border-radius: 12px;
  padding: 1rem;
}

.title {
  padding: 0;
  font-weight: 500;
  font-size: 12px;
}

.ct {
  margin-left: 5px;
}

#volume-widget-icon {
  margin-top: -24px;
  margin-right: -30px;
  color: var(--shadow);
}

#brightness-widget-icon {
  margin-top: -24px;
  margin-right: -25px;
  color: alpha(#111, 0.6);
}

#control-center-menu slider {
  background-image: none;
  background-color: transparent;
  padding: 2px;
  min-width: 4px;
  min-height: 20px;
  border-radius: 20px;
}

#control-center-menu scale {
  background-color: transparent;
  margin-top: 10px;
  border-radius: 20px;
}

#control-center-menu trough {
  min-width: 25px;
  border-radius: 99px;
  background-color: alpha(#666, 0.5);
  border: 1px solid alpha(#444, 0.3);
}

#control-center-menu highlight {
  background: alpha(#fff, 0.8);
  border-radius: 99px;
}

#control-center-menu mark indicator {
  background: none;
  background-image: none;
  color: alpha(#fff, 0.2);
}

#control-center-menu mark label {
  background: none;
  background-image: none;
  color: alpha(#fff, 0.2);
}