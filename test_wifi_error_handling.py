#!/usr/bin/env python3
"""
Test script demonstrating WiFi password dialog error handling
"""

import gi
gi.require_version("Gtk", "3.0")
from gi.repository import Gtk, GLib

from fabric import Application
from widgets.wifi_password_dialog import WiFiPasswordDialog


class WiFiErrorDemo:
    def __init__(self):
        self.dialog = None
        self.attempt_count = 0
    
    def on_connect(self, ssid, password):
        self.attempt_count += 1
        print(f"🔄 Attempt #{self.attempt_count}: Connecting to '{ssid}' with password: '{password}'")
        
        # Simulate different scenarios
        if password.lower() == "correct":
            print("✅ Connection successful! Dialog closed.")
            Gtk.main_quit()
        elif password.lower() == "quit":
            print("👋 Quitting demo...")
            Gtk.main_quit()
        else:
            print("❌ Incorrect password - showing error and reopening dialog")
            # Simulate the error handling that would happen in real WiFi connection
            GLib.timeout_add(500, lambda: self.show_error())
    
    def show_error(self):
        """Show error message in dialog"""
        if self.dialog:
            error_messages = [
                "Incorrect password. Please try again.",
                "Authentication failed. Check your password.",
                "Wrong password. Please verify and try again."
            ]
            message = error_messages[min(self.attempt_count - 1, len(error_messages) - 1)]
            self.dialog.show_error(message)
        return False
    
    def on_cancel(self):
        print("❌ Connection cancelled by user")
        Gtk.main_quit()
    
    def start_demo(self):
        print("🔧 WiFi Password Dialog Error Handling Demo")
        print("=" * 50)
        print("📝 Test scenarios:")
        print("   • Enter 'correct' → Connection succeeds, dialog closes")
        print("   • Enter anything else → Shows error, dialog reopens")
        print("   • Enter 'quit' → Exits demo")
        print("   • Click Cancel or press Escape → Cancels")
        print("👁️  Click eye icon to toggle password visibility")
        print("⌨️  Press Enter to connect")
        print("=" * 50)
        
        self.dialog = WiFiPasswordDialog(
            ssid="TestNetwork_5G",
            on_connect_callback=self.on_connect,
            on_cancel_callback=self.on_cancel
        )
        
        self.dialog.show_dialog()
        Gtk.main()


if __name__ == "__main__":
    demo = WiFiErrorDemo()
    demo.start_demo()
