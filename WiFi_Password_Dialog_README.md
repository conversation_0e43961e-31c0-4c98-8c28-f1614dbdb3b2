# WiFi Password Dialog

A macOS-style WiFi password dialog for the envShell desktop environment.

## Features

- **macOS-inspired design** with translucent background and blur effects
- **Password visibility toggle** with a switch control
- **Keyboard shortcuts** (Enter to connect, Escape to cancel)
- **Auto-focus** on password entry when dialog opens
- **Input validation** to prevent empty password submission
- **Clean integration** with the existing WiFi control center widget

## Components

### WiFiPasswordDialog (`widgets/wifi_password_dialog.py`)

The main dialog class that provides:
- WiFi icon display
- Network name in the title
- Password entry field with visibility toggle
- Cancel and Join buttons
- Keyboard event handling
- Callback system for connect/cancel actions

### Integration with WiFi Control Center

The dialog is integrated into the existing WiFi control center widget (`modules/controlcenter/wifi.py`):
- Automatically shows when a network connection fails (likely needs password)
- Handles the connection attempt with the entered password
- Provides visual feedback during connection process

## Usage

### Basic Usage

```python
from widgets.wifi_password_dialog import WiFiPasswordDialog

def on_connect(ssid, password):
    print(f"Connect to {ssid} with password: {password}")
    # Handle connection logic here

def on_cancel():
    print("Connection cancelled")

# Create and show dialog
dialog = WiFiPasswordDialog(
    ssid="MyNetwork",
    on_connect_callback=on_connect,
    on_cancel_callback=on_cancel
)
dialog.show_dialog()
```

### Integration Example

The dialog is automatically triggered in the WiFi control center when:
1. User clicks on a WiFi network
2. Initial connection attempt fails (no saved password)
3. Dialog appears requesting password input
4. User enters password and clicks "Join" or presses Enter
5. Connection attempt is made with the provided password

## Styling

The dialog uses CSS styling defined in `styles/controlcenter.css`:

- **Background**: Translucent dark background with blur effect
- **Border**: Subtle border with rounded corners
- **Typography**: Clean, readable fonts matching macOS style
- **Colors**: Blue accent color for the Join button and focus states
- **Animations**: Smooth transitions and hover effects

## Keyboard Shortcuts

- **Enter**: Connect with entered password
- **Escape**: Cancel and close dialog
- **Tab**: Navigate between fields

## Testing

Two test scripts are provided:

1. **`test_wifi_dialog.py`**: Simple standalone test of the dialog
2. **`demo_wifi_integration.py`**: Full demo showing integration with multiple networks

Run either script to see the dialog in action:

```bash
python test_wifi_dialog.py
# or
python demo_wifi_integration.py
```

## Design Decisions

### User Experience
- **Auto-focus**: Password field is automatically focused when dialog opens
- **Validation**: Empty passwords are rejected with focus return to entry
- **Clean state**: New dialog instance created each time for clean state
- **Responsive**: Dialog responds to both mouse and keyboard input

### Visual Design
- **macOS-like**: Closely matches macOS WiFi password dialog appearance
- **Accessibility**: Good contrast ratios and readable text sizes
- **Consistency**: Matches the overall envShell design language
- **Modern**: Uses blur effects and translucent backgrounds

### Technical Implementation
- **Wayland compatible**: Uses WaylandWindow for proper layer shell integration
- **Memory efficient**: Proper dialog lifecycle management
- **Event-driven**: Callback-based architecture for clean separation of concerns
- **Extensible**: Easy to modify or extend for additional features

## Future Enhancements

Potential improvements that could be added:

1. **Remember password option**: Checkbox to save password for future connections
2. **Network security type detection**: Show appropriate security type (WPA2, WPA3, etc.)
3. **Connection progress indicator**: Show progress during connection attempt
4. **Error handling**: Display specific error messages for connection failures
5. **Advanced options**: Enterprise network settings, proxy configuration
6. **Accessibility**: Screen reader support and keyboard navigation improvements

## Dependencies

- **Fabric**: Desktop widget framework
- **GTK3**: GUI toolkit
- **PyGObject**: Python GTK bindings
- **GtkLayerShell**: Wayland layer shell protocol support

The dialog integrates seamlessly with the existing envShell infrastructure and follows the established patterns for widget development.
