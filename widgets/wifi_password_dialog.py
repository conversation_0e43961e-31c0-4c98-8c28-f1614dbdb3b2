import gi
from gi.repository import Gtk, Gdk, <PERSON><PERSON><PERSON>
from fabric.widgets.box import Box
from fabric.widgets.button import Button
from fabric.widgets.centerbox import CenterBox
from fabric.widgets.entry import Entry
from fabric.widgets.image import Image
from fabric.widgets.label import Label
from fabric.widgets.revealer import Revealer


from widgets.wayland import WaylandWindow

gi.require_version("Gtk", "3.0")


class WiFiPasswordDialog(WaylandWindow):
    """
    A macOS-style WiFi password dialog window.
    """
    
    def __init__(self, ssid: str, on_connect_callback=None, on_cancel_callback=None, **kwargs):
        super().__init__(
            title="modus",
            layer="overlay",
            anchor="center",
            keyboard_mode="on-demand",
            visible=False,
            name="wifi-password-dialog",
            **kwargs
        )
        
        self.ssid = ssid
        self.on_connect_callback = on_connect_callback
        self.on_cancel_callback = on_cancel_callback
        
        # Create the dialog content
        self._create_dialog_content()
        
        # Connect keyboard events
        self.connect("key-press-event", self._on_key_press)
        
        # Focus the password entry when shown
        self.connect("notify::visible", self._on_visibility_changed)
    
    def _create_dialog_content(self):
        """Create the main dialog content"""
        
        # WiFi icon
        self.wifi_icon = Image(
            icon_name="network-wireless-symbolic",
            size=48,
            name="wifi-dialog-icon"
        )
        
        # Title text
        self.title_label = Label(
            label=f'The Wi-Fi network "{self.ssid}" requires a WPA2 password.',
            name="wifi-dialog-title",
            h_align="center",
            wrap=True,
            max_width_chars=40
        )

        # Error message label (initially hidden)
        self.error_label = Label(
            label="Incorrect password. Please try again.",
            name="wifi-dialog-error",
            h_align="center",
            visible=False
        )
        
        # Password label
        self.password_label = Label(
            label="Password:",
            name="wifi-dialog-password-label",
            h_align="start"
        )
        
        # Password entry
        self.password_entry = Entry(
            placeholder_text="Enter password",
            name="wifi-dialog-password-entry",
            visibility=False,  # Hide password by default
            h_expand=True
        )
        # Connect Enter key to join action
        self.password_entry.connect("activate", lambda *_: self._on_join_clicked())
        
        # Show password toggle button with eye icon
        self.password_visible = False
        self.show_password_button = Button(
            image=Image(
                icon_name="view-conceal-symbolic",
                size=16
            ),
            name="wifi-dialog-show-password-button",
            on_clicked=self._on_show_password_clicked
        )

        self.show_password_label = Label(
            label="Show password",
            name="wifi-dialog-show-password-label"
        )

        # Show password container
        self.show_password_box = Box(
            orientation="h",
            spacing=8,
            children=[
                self.show_password_button,
                self.show_password_label
            ],
            name="wifi-dialog-show-password-box"
        )
        
        # Buttons
        self.cancel_button = Button(
            label="Cancel",
            name="wifi-dialog-cancel-button",
            on_clicked=self._on_cancel_clicked
        )
        
        self.join_button = Button(
            label="Join",
            name="wifi-dialog-join-button",
            on_clicked=self._on_join_clicked
        )
        
        # Button container
        self.button_box = Box(
            orientation="h",
            spacing=12,
            children=[
                self.cancel_button,
                self.join_button
            ],
            name="wifi-dialog-button-box",
            h_align="end"
        )
        
        # Password input container
        self.password_container = Box(
            orientation="v",
            spacing=8,
            children=[
                self.password_label,
                self.password_entry,
                self.show_password_box
            ],
            name="wifi-dialog-password-container"
        )
        
        # Main content container
        self.content_box = Box(
            orientation="v",
            spacing=16,
            children=[
                self.wifi_icon,
                self.title_label,
                self.error_label,
                self.password_container,
                self.button_box
            ],
            name="wifi-dialog-content",
            h_align="center",
            v_align="center"
        )
        
        # Dialog background
        self.dialog_background = Box(
            children=[self.content_box],
            name="wifi-dialog-background",
            h_align="center",
            v_align="center"
        )
        
        self.children = self.dialog_background
    
    def _on_show_password_clicked(self, *args):
        """Toggle password visibility"""
        self.password_visible = not self.password_visible
        self.password_entry.set_visibility(self.password_visible)

        # Update icon based on visibility state
        icon_name = "view-reveal-symbolic" if self.password_visible else "view-conceal-symbolic"
        self.show_password_button.get_image().set_property("icon-name", icon_name)
    
    def _on_key_press(self, widget, event):
        """Handle keyboard events"""
        keyval = event.keyval
        
        if keyval == Gdk.KEY_Return or keyval == Gdk.KEY_KP_Enter:
            # Enter key - connect
            self._on_join_clicked()
            return True
        elif keyval == Gdk.KEY_Escape:
            # Escape key - cancel
            self._on_cancel_clicked()
            return True
        
        return False
    
    def _on_visibility_changed(self, widget, *args):
        """Handle visibility changes"""
        if self.get_visible():
            # Focus the password entry when dialog becomes visible
            GLib.timeout_add(100, lambda: self.password_entry.grab_focus())
    
    def _on_cancel_clicked(self, *args):
        """Handle cancel button click"""
        self.hide()
        if self.on_cancel_callback:
            self.on_cancel_callback()
    
    def _on_join_clicked(self, *args):
        """Handle join button click"""
        password = self.password_entry.get_text().strip()

        # Don't connect if password is empty
        if not password:
            # Focus back to password entry
            self.password_entry.grab_focus()
            return

        # Hide error message if visible
        self.error_label.set_visible(False)

        # Always close the dialog when Join is clicked
        self.hide()
        if self.on_connect_callback:
            self.on_connect_callback(self.ssid, password)
    
    def show_dialog(self):
        """Show the dialog and focus the password entry"""
        self.show_all()
        self.password_entry.set_text("")  # Clear any previous password
        self.error_label.set_visible(False)  # Hide error message

        # Reset password visibility state
        self.password_visible = False
        self.password_entry.set_visibility(False)
        self.show_password_button.get_image().set_property("icon-name", "view-conceal-symbolic")

    def show_error(self, message="Incorrect password. Please try again."):
        """Show error message and reopen dialog"""
        self.error_label.set_text(message)
        self.error_label.set_visible(True)
        self.show_all()
        # Focus password entry and select all text for easy replacement
        GLib.timeout_add(100, lambda: (
            self.password_entry.grab_focus(),
            self.password_entry.select_region(0, -1)
        ))
    
    def get_password(self):
        """Get the entered password"""
        return self.password_entry.get_text()

    def destroy_dialog(self):
        """Properly destroy the dialog"""
        self.hide()
        self.destroy()
