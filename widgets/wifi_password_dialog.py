import gi
from gi.repository import Gtk, Gdk, <PERSON><PERSON><PERSON>
from fabric.widgets.box import Box
from fabric.widgets.button import Button
from fabric.widgets.centerbox import CenterBox
from fabric.widgets.entry import Entry
from fabric.widgets.image import Image
from fabric.widgets.label import Label
from fabric.widgets.revealer import Revealer
from fabric.widgets.switch import Switch

from widgets.wayland import WaylandWindow

gi.require_version("Gtk", "3.0")


class WiFiPasswordDialog(WaylandWindow):
    """
    A macOS-style WiFi password dialog window.
    """
    
    def __init__(self, ssid: str, on_connect_callback=None, on_cancel_callback=None, **kwargs):
        super().__init__(
            title="WiFi Password",
            layer="overlay",
            anchor="center",
            keyboard_mode="on-demand",
            visible=False,
            name="wifi-password-dialog",
            **kwargs
        )
        
        self.ssid = ssid
        self.on_connect_callback = on_connect_callback
        self.on_cancel_callback = on_cancel_callback
        
        # Create the dialog content
        self._create_dialog_content()
        
        # Connect keyboard events
        self.connect("key-press-event", self._on_key_press)
        
        # Focus the password entry when shown
        self.connect("notify::visible", self._on_visibility_changed)
    
    def _create_dialog_content(self):
        """Create the main dialog content"""
        
        # WiFi icon
        self.wifi_icon = Image(
            icon_name="network-wireless-symbolic",
            size=48,
            name="wifi-dialog-icon"
        )
        
        # Title text
        self.title_label = Label(
            label=f'The Wi-Fi network "{self.ssid}" requires a WPA2 password.',
            name="wifi-dialog-title",
            h_align="center",
            wrap=True,
            max_width_chars=40
        )
        
        # Password label
        self.password_label = Label(
            label="Password:",
            name="wifi-dialog-password-label",
            h_align="start"
        )
        
        # Password entry
        self.password_entry = Entry(
            placeholder_text="Enter password",
            name="wifi-dialog-password-entry",
            visibility=False,  # Hide password by default
            h_expand=True
        )
        
        # Show password checkbox
        self.show_password_switch = Switch(
            name="wifi-dialog-show-password",
            active=False
        )
        self.show_password_switch.connect("notify::active", self._on_show_password_toggled)
        
        self.show_password_label = Label(
            label="Show password",
            name="wifi-dialog-show-password-label"
        )
        
        # Show password container
        self.show_password_box = Box(
            orientation="h",
            spacing=8,
            children=[
                self.show_password_switch,
                self.show_password_label
            ],
            name="wifi-dialog-show-password-box"
        )
        
        # Buttons
        self.cancel_button = Button(
            label="Cancel",
            name="wifi-dialog-cancel-button",
            on_clicked=self._on_cancel_clicked
        )
        
        self.join_button = Button(
            label="Join",
            name="wifi-dialog-join-button",
            on_clicked=self._on_join_clicked
        )
        
        # Button container
        self.button_box = Box(
            orientation="h",
            spacing=12,
            children=[
                self.cancel_button,
                self.join_button
            ],
            name="wifi-dialog-button-box",
            h_align="end"
        )
        
        # Password input container
        self.password_container = Box(
            orientation="v",
            spacing=8,
            children=[
                self.password_label,
                self.password_entry,
                self.show_password_box
            ],
            name="wifi-dialog-password-container"
        )
        
        # Main content container
        self.content_box = Box(
            orientation="v",
            spacing=16,
            children=[
                self.wifi_icon,
                self.title_label,
                self.password_container,
                self.button_box
            ],
            name="wifi-dialog-content",
            h_align="center",
            v_align="center"
        )
        
        # Dialog background
        self.dialog_background = Box(
            children=[self.content_box],
            name="wifi-dialog-background",
            h_align="center",
            v_align="center"
        )
        
        self.children = self.dialog_background
    
    def _on_show_password_toggled(self, switch, *args):
        """Toggle password visibility"""
        show_password = switch.get_active()
        self.password_entry.set_visibility(show_password)
    
    def _on_key_press(self, widget, event):
        """Handle keyboard events"""
        keyval = event.keyval
        
        if keyval == Gdk.KEY_Return or keyval == Gdk.KEY_KP_Enter:
            # Enter key - connect
            self._on_join_clicked()
            return True
        elif keyval == Gdk.KEY_Escape:
            # Escape key - cancel
            self._on_cancel_clicked()
            return True
        
        return False
    
    def _on_visibility_changed(self, widget, *args):
        """Handle visibility changes"""
        if self.get_visible():
            # Focus the password entry when dialog becomes visible
            GLib.timeout_add(100, lambda: self.password_entry.grab_focus())
    
    def _on_cancel_clicked(self, *args):
        """Handle cancel button click"""
        self.hide()
        if self.on_cancel_callback:
            self.on_cancel_callback()
    
    def _on_join_clicked(self, *args):
        """Handle join button click"""
        password = self.password_entry.get_text()
        self.hide()
        if self.on_connect_callback:
            self.on_connect_callback(self.ssid, password)
    
    def show_dialog(self):
        """Show the dialog and focus the password entry"""
        self.show_all()
        self.password_entry.set_text("")  # Clear any previous password
        self.show_password_switch.set_active(False)  # Reset show password state
    
    def get_password(self):
        """Get the entered password"""
        return self.password_entry.get_text()
