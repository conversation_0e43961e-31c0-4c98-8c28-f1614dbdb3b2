#!/usr/bin/env python3
"""
Test script to verify incorrect passwords are not saved to NetworkManager
"""

import subprocess
import time
import gi
gi.require_version("Gtk", "3.0")
from gi.repository import Gtk, GLib

from fabric import Application
from widgets.wifi_password_dialog import WiFiPasswordDialog
from services.network import NetworkService


class PasswordSaveTest:
    def __init__(self):
        self.dialog = None
        self.network_service = NetworkService()
        self.test_ssid = "TestNetwork_DoNotSave"
        self.attempt_count = 0
    
    def check_saved_connections(self):
        """Check if the test SSID is saved in NetworkManager"""
        try:
            result = subprocess.run(
                ["nmcli", "-t", "-f", "NAME", "connection", "show"],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                connections = result.stdout.strip().split('\n')
                saved_connections = [conn for conn in connections if self.test_ssid in conn]
                return saved_connections
            return []
        except Exception as e:
            print(f"Error checking connections: {e}")
            return []
    
    def cleanup_test_connections(self):
        """Clean up any test connections"""
        try:
            subprocess.run(
                ["nmcli", "connection", "delete", self.test_ssid],
                capture_output=True
            )
            # Also clean up any temporary connections
            result = subprocess.run(
                ["nmcli", "-t", "-f", "NAME", "connection", "show"],
                capture_output=True,
                text=True
            )
            if result.returncode == 0:
                connections = result.stdout.strip().split('\n')
                for conn in connections:
                    if "temp_" in conn and self.test_ssid in conn:
                        subprocess.run(
                            ["nmcli", "connection", "delete", conn],
                            capture_output=True
                        )
        except:
            pass
    
    def on_connect(self, ssid, password):
        self.attempt_count += 1
        print(f"\n🔄 Attempt #{self.attempt_count}: Testing password '{password}' for '{ssid}'")
        
        # Check connections before attempt
        before_connections = self.check_saved_connections()
        print(f"📋 Saved connections before attempt: {len(before_connections)}")
        
        # Test the connection (this will fail for non-existent network)
        success = self.network_service.wifi_device.connect_network(ssid, password, remember=True)
        
        # Wait a moment for NetworkManager to process
        time.sleep(2)
        
        # Check connections after attempt
        after_connections = self.check_saved_connections()
        print(f"📋 Saved connections after attempt: {len(after_connections)}")
        
        if success:
            print("✅ Connection successful!")
            if len(after_connections) > len(before_connections):
                print("💾 Password was saved (expected for successful connection)")
            Gtk.main_quit()
        else:
            print("❌ Connection failed (expected)")
            if len(after_connections) == len(before_connections):
                print("🎉 SUCCESS: Incorrect password was NOT saved!")
            else:
                print("⚠️  WARNING: Incorrect password might have been saved!")
                print(f"   New connections: {after_connections}")
            
            if password.lower() == "quit":
                print("👋 Exiting test...")
                Gtk.main_quit()
            else:
                # Show error dialog
                if self.dialog:
                    GLib.timeout_add(1000, lambda: self.dialog.show_error(
                        f"Test #{self.attempt_count}: Password not saved. Try 'quit' to exit."
                    ))
    
    def on_cancel(self):
        print("❌ Test cancelled")
        self.cleanup_test_connections()
        Gtk.main_quit()
    
    def start_test(self):
        print("🧪 Testing Password Save Prevention")
        print("=" * 60)
        print("📋 This test verifies that incorrect passwords are NOT saved to NetworkManager")
        print("🎯 Test procedure:")
        print("   1. Enter any incorrect password")
        print("   2. Check if it gets saved to NetworkManager")
        print("   3. Verify only successful passwords are saved")
        print("💡 Tips:")
        print("   • Enter 'quit' to exit test")
        print("   • Try multiple wrong passwords to verify none are saved")
        print("   • Check the console output for connection counts")
        print("=" * 60)
        
        # Clean up any existing test connections
        self.cleanup_test_connections()
        
        # Show initial connection count
        initial_connections = self.check_saved_connections()
        print(f"📊 Initial saved connections for '{self.test_ssid}': {len(initial_connections)}")
        
        self.dialog = WiFiPasswordDialog(
            ssid=self.test_ssid,
            on_connect_callback=self.on_connect,
            on_cancel_callback=self.on_cancel
        )
        
        self.dialog.show_dialog()
        
        try:
            Gtk.main()
        finally:
            # Clean up after test
            self.cleanup_test_connections()
            print("\n🧹 Cleaned up test connections")


if __name__ == "__main__":
    test = PasswordSaveTest()
    test.start_test()
