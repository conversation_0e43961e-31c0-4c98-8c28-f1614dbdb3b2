#!/usr/bin/env python3
"""
Test script for the WiFi password dialog with eye icon toggle
"""

import gi
gi.require_version("Gtk", "3.0")
from gi.repository import Gtk, GLib

from fabric import Application
from widgets.wifi_password_dialog import WiFiPasswordDialog


def on_connect(ssid, password):
    print(f"🔄 Attempting to connect to '{ssid}' with password: '{password}'")

    # Simulate password validation (for demo purposes)
    if password.lower() == "correct":
        print("✅ Connection successful!")
        Gtk.main_quit()
    else:
        print("❌ Incorrect password - showing error message")
        # In real implementation, this would be handled by the network service
        # For demo, we'll show how the error handling works
        dialog = None
        for widget in Gtk.Window.list_toplevels():
            if hasattr(widget, 'show_error'):
                dialog = widget
                break

        if dialog:
            GLib.timeout_add(1000, lambda: dialog.show_error("Incorrect password. Please try again."))


def on_cancel():
    print("❌ Connection cancelled by user")
    Gtk.main_quit()


def main():
    print("🔧 Testing WiFi Password Dialog with Error Handling")
    print("👁️  Click the eye icon to toggle password visibility")
    print("⌨️  Press Enter to connect, Escape to cancel")
    print("🔑 Enter 'correct' as password to succeed, anything else to see error")
    print("❌ Dialog closes on Join click, shows error if password is wrong")
    print("=" * 60)

    # Create and show the dialog
    dialog = WiFiPasswordDialog(
        ssid="free_wifi@DH",
        on_connect_callback=on_connect,
        on_cancel_callback=on_cancel
    )

    dialog.show_dialog()

    # Start the GTK main loop
    Gtk.main()


if __name__ == "__main__":
    main()
