#!/usr/bin/env python3
"""
Demo script showing WiFi password dialog integration
"""

import gi
gi.require_version("Gtk", "3.0")
from gi.repository import Gtk, GLib

from fabric import Application
from fabric.widgets.box import Box
from fabric.widgets.button import Button
from fabric.widgets.label import Label
from widgets.wayland import WaylandWindow
from widgets.wifi_password_dialog import WiFiPasswordDialog


class WiFiDemo(WaylandWindow):
    def __init__(self):
        super().__init__(
            title="WiFi Demo",
            layer="top",
            anchor="center",
            keyboard_mode="on-demand",
            visible=True,
            name="wifi-demo"
        )
        
        self.demo_networks = [
            "free_wifi@DH",
            "MyHomeNetwork",
            "CoffeeShop_WiFi",
            "SecureNetwork_5G"
        ]
        
        self.create_demo_ui()
    
    def create_demo_ui(self):
        """Create the demo UI"""
        title = Label(
            label="WiFi Password Dialog Demo",
            name="demo-title"
        )
        
        subtitle = Label(
            label="Click on any network to see the password dialog",
            name="demo-subtitle"
        )
        
        # Create network buttons
        network_buttons = []
        for network in self.demo_networks:
            btn = Button(
                label=f"Connect to {network}",
                name="demo-network-button",
                on_clicked=lambda *_, ssid=network: self.show_password_dialog(ssid)
            )
            network_buttons.append(btn)
        
        # Main container
        self.children = Box(
            orientation="v",
            spacing=16,
            children=[
                title,
                subtitle,
                *network_buttons
            ],
            name="demo-container"
        )
    
    def show_password_dialog(self, ssid):
        """Show password dialog for the given SSID"""
        dialog = WiFiPasswordDialog(
            ssid=ssid,
            on_connect_callback=self.on_connect,
            on_cancel_callback=self.on_cancel
        )
        dialog.show_dialog()
    
    def on_connect(self, ssid, password):
        """Handle connection attempt"""
        print(f"Attempting to connect to '{ssid}' with password: '{password}'")
        # In real implementation, this would call the network service
        
    def on_cancel(self):
        """Handle connection cancellation"""
        print("Connection cancelled by user")


def main():
    # Set up CSS styling
    css = """
    #demo-container {
        background-color: rgba(40, 40, 40, 0.95);
        border-radius: 12px;
        padding: 24px;
        margin: 20px;
    }
    
    #demo-title {
        color: #ffffff;
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 8px;
    }
    
    #demo-subtitle {
        color: #aaaaaa;
        font-size: 14px;
        margin-bottom: 16px;
    }
    
    #demo-network-button {
        background-color: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 6px;
        padding: 12px 16px;
        color: #ffffff;
        font-size: 14px;
        margin: 4px 0;
    }
    
    #demo-network-button:hover {
        background-color: rgba(255, 255, 255, 0.15);
    }
    """
    
    # Create application
    demo = WiFiDemo()
    app = Application("wifi-demo", demo)
    app.set_stylesheet(css)
    app.run()


if __name__ == "__main__":
    main()
