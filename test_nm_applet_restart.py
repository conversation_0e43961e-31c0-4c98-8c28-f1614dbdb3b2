#!/usr/bin/env python3
"""
Test script to verify nm-applet is properly restarted after password dialog operations
"""

import subprocess
import time
import gi
gi.require_version("Gtk", "3.0")
from gi.repository import Gtk, GLib

from fabric import Application
from widgets.wifi_password_dialog import WiFiPasswordDialog
from services.network import NetworkService


class NMAppletRestartTest:
    def __init__(self):
        self.dialog = None
        self.network_service = NetworkService()
        self.test_ssid = "TestNetwork_NMApplet"
        self.initial_nm_applet_running = False
    
    def is_nm_applet_running(self):
        """Check if nm-applet is currently running"""
        try:
            result = subprocess.run(
                ["pgrep", "-f", "nm-applet"],
                capture_output=True,
                text=True
            )
            return result.returncode == 0
        except Exception:
            return False
    
    def get_nm_applet_count(self):
        """Get the number of nm-applet processes running"""
        try:
            result = subprocess.run(
                ["pgrep", "-f", "nm-applet"],
                capture_output=True,
                text=True
            )
            if result.returncode == 0:
                return len(result.stdout.strip().split('\n'))
            return 0
        except Exception:
            return 0
    
    def on_connect(self, ssid, password):
        print(f"\n🔄 Testing connection to '{ssid}' with password: '{password}'")
        
        # Check nm-applet status before connection attempt
        before_running = self.is_nm_applet_running()
        before_count = self.get_nm_applet_count()
        print(f"📊 Before connection: nm-applet running={before_running}, count={before_count}")
        
        # Test the connection (this will kill and restart nm-applet)
        success = self.network_service.wifi_device.connect_network(ssid, password, remember=False)
        
        # Wait a moment for the restart process to complete
        time.sleep(2)
        
        # Check nm-applet status after connection attempt
        after_running = self.is_nm_applet_running()
        after_count = self.get_nm_applet_count()
        print(f"📊 After connection: nm-applet running={after_running}, count={after_count}")
        
        # Analyze the results
        if self.initial_nm_applet_running:
            if after_running:
                print("✅ SUCCESS: nm-applet was properly restarted!")
                if after_count == 1:
                    print("✅ BONUS: Only one nm-applet instance running (no duplicates)")
                else:
                    print(f"⚠️  WARNING: {after_count} nm-applet instances running")
            else:
                print("❌ FAILURE: nm-applet was not restarted!")
        else:
            if not after_running:
                print("✅ SUCCESS: nm-applet correctly remained stopped (wasn't running initially)")
            else:
                print("⚠️  INFO: nm-applet was started even though it wasn't running initially")
        
        if password.lower() == "quit":
            print("👋 Exiting test...")
            Gtk.main_quit()
        else:
            # Show error dialog for next test
            if self.dialog:
                GLib.timeout_add(1000, lambda: self.dialog.show_error(
                    f"Test complete. Try 'quit' to exit or test again."
                ))
    
    def on_cancel(self):
        print("❌ Test cancelled")
        Gtk.main_quit()
    
    def start_test(self):
        print("🧪 Testing nm-applet Restart After Password Dialog")
        print("=" * 60)
        print("📋 This test verifies that nm-applet is properly restarted after WiFi operations")
        
        # Check initial nm-applet status
        self.initial_nm_applet_running = self.is_nm_applet_running()
        initial_count = self.get_nm_applet_count()
        
        print(f"📊 Initial state: nm-applet running={self.initial_nm_applet_running}, count={initial_count}")
        
        if not self.initial_nm_applet_running:
            print("⚠️  WARNING: nm-applet is not currently running!")
            print("💡 You may want to start it first: nm-applet --indicator &")
            print("🔄 Continuing test anyway...")
        
        print("\n🎯 Test procedure:")
        print("   1. Enter any password (will fail for non-existent network)")
        print("   2. Check if nm-applet is killed during connection attempt")
        print("   3. Verify nm-applet is restarted after attempt")
        print("💡 Tips:")
        print("   • Enter 'quit' to exit test")
        print("   • Watch system tray for nm-applet icon changes")
        print("   • Check console output for detailed status")
        print("=" * 60)
        
        self.dialog = WiFiPasswordDialog(
            ssid=self.test_ssid,
            on_connect_callback=self.on_connect,
            on_cancel_callback=self.on_cancel
        )
        
        self.dialog.show_dialog()
        Gtk.main()


if __name__ == "__main__":
    test = NMAppletRestartTest()
    test.start_test()
