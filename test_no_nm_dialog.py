#!/usr/bin/env python3
"""
Test script to verify NetworkManager dialogs don't appear
"""

import gi
gi.require_version("Gtk", "3.0")
from gi.repository import Gtk, GLib

from fabric import Application
from widgets.wifi_password_dialog import WiFiPasswordDialog
from services.network import NetworkService


class NoDialogTest:
    def __init__(self):
        self.dialog = None
        self.network_service = NetworkService()
        self.attempt_count = 0
    
    def on_connect(self, ssid, password):
        self.attempt_count += 1
        print(f"🔄 Attempt #{self.attempt_count}: Testing connection to '{ssid}' with password: '{password}'")
        print("🚫 Checking if NetworkManager dialog appears...")
        
        # Test the actual network connection
        success = self.network_service.wifi_device.connect_network(ssid, password, remember=False)
        
        if success:
            print("✅ Connection successful! No NetworkManager dialog should have appeared.")
            print("🎉 Test passed - NetworkManager dialogs are suppressed!")
            Gtk.main_quit()
        else:
            print("❌ Connection failed (expected for wrong password)")
            print("🔍 Did you see any NetworkManager password dialog? If not, test passed!")
            
            # Show our custom error dialog instead
            if self.dialog:
                GLib.timeout_add(1000, lambda: self.dialog.show_error(
                    f"Connection failed (attempt {self.attempt_count}). Try 'quit' to exit."
                ))
    
    def on_cancel(self):
        print("❌ Test cancelled by user")
        Gtk.main_quit()
    
    def start_test(self):
        print("🧪 Testing NetworkManager Dialog Suppression")
        print("=" * 50)
        print("📋 This test verifies that NetworkManager password dialogs don't appear")
        print("🎯 Expected behavior:")
        print("   • Enter wrong password → No NetworkManager dialog appears")
        print("   • Only our custom dialog should show error messages")
        print("   • Enter 'quit' to exit test")
        print("⚠️  Note: This test uses actual network connections!")
        print("=" * 50)
        
        # Use a non-existent network to ensure failure
        test_ssid = "NonExistentTestNetwork_12345"
        
        self.dialog = WiFiPasswordDialog(
            ssid=test_ssid,
            on_connect_callback=self.on_connect,
            on_cancel_callback=self.on_cancel
        )
        
        self.dialog.show_dialog()
        Gtk.main()


if __name__ == "__main__":
    test = NoDialogTest()
    test.start_test()
